
| TABLE_NAME | COLUMN_NAME | CONSTRAINT_NAME | REFERENCED_TABLE_NAME | REFERENCED_COLUMN_NAME |
|------------|-------------|-----------------|----------------------|------------------------|
| itemclient | identity | itemclient_identity_fk | entity | identity |
| historical | iduser | historical_iduser_fk | entity | identity |
| assessment | idteacher | assessment_idteacher_fk | entity | identity |
| content_teachers | idteacher | content_teachers_idteacher_fk | entity | identity |
| studentgrade | idstudent | studentgrade_idstudent_fk | entity | identity |
| historicaldocument | identity | historicaldocument_identity_fk | entity | identity |
| message_for | idreceive | message_for_idreceive_fk | entity | identity |
| admin | identity | admin_identity_fk | entity | identity |
| admin | lastposentity | admin_lastposentity_fk | entity | identity |
| documentprovider | idprovider | documentprovider_idprovider_fk | entity | identity |
| historicalprint | iduser | historicalprint_iduser_fk | entity | identity |
| teacher | identity | teacher_identity_fk | entity | identity |
| assessmentgrades | idstudent | assessmentgrades_idstudent_fk | entity | identity |
| teacher_faults | idteacher | teacher_faults_idteacher_fk | entity | identity |
| access | identity | access_identity_fk | entity | identity |
| teacheravailability | idteacher | teacheravailability_idteacher_fk | entity | identity |
| teacherpayment | idteacher | teacherpayment_idteacher_fk | entity | identity |
| entity_address | identity | entity_address_identity_fk | entity | identity |
| report | idstudent | report_idstudent_fk | entity | identity |
| report | idteacher | report_idteacher_fk | entity | identity |
| entities_docs | identity | entities_docs_identity_fk | entity | identity |
| statemessage | identity | statemessage_identity_fk | entity | identity |
| studentabsences_historic | idstudent | student_absences_idstudent_fk | entity | identity |
| message | idsend | message_idsend_fk | entity | identity |
| registrationstudent_class | idstudent | registrationstudent_class_idstudent_fk | entity | identity |
| ativitystudent | identity | ativitystudent_idstudent_fk | entity | identity |
| servicefood | idstudent | servicefood_idstudent_fk | entity | identity |
| admin_role | identity | admin_role_identity_fk | entity | identity |
| classes_teacher | idteacher | classes_teacher_idteacher_fk | entity | identity |
| student | identity | student_identity_fk | entity | identity |
| student | idstudentparent2 | student_idstudentparent2_fk | entity | identity |
| student | idstudentparent | student_idstudentparent_fk | entity | identity |
| events | identity | events_identity_fk | entity | identity |
| historicwebservice | iduser | historicwebservice_iduser_fk | entity | identity |
| teacheravailability_dayweek | idteacher | teacheravailability_dayweek_idteacher_fk | entity | identity |
| didaticmaterial_student | idstudent | didaticmaterial_student_idstudent_fk | entity | identity |
| saft | iduser | saft_iduser_fk | entity | identity |
| block | idteacher | block_idteacher_fk | entity | identity |
| blockteachers | idteacher | blockteachers_idteacher_fk | entity | identity |
| servicetransport | idstudent | servicetransport_idstudent_fk | entity | identity |
| bankaccount | identity | bankaccount_identity_fk | entity | identity |
| summaries_notes | idstudent | summaries_notes_idstudent_fk | entity | identity |
| provider | identity | provider_identity_fk | entity | identity |
| studentparent | identity | studentparent_identity_fk | entity | identity |
| studentparent | identitycompany | studentparent_identitycompany_fk | entity | identity |
| registrationstudent | idstudent | registrationstudent_idstudent_fk | entity | identity |
| studentclass_realtimevideo | identity | studentclass_realtimevideo_identity_fk | entity | identity |
| itemlistaux | identity | itemlistaux_identity_fk | entity | identity |
| itemlistaux | idstudent | itemlistaux_idstudent_fk | entity | identity |
| studentclass_pedagogicalcriteria | identity | studentclass_pedagogicalcriteria_identity_fk | entity | identity |
| historicaldocument | idstudent | historicaldocument_idstudent_fk | entity | identity |
